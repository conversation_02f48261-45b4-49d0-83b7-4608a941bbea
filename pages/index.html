<!DOCTYPE html>
<html lang="de-DE">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DAK Service Portal</title>
    <link rel="stylesheet" href="../styles/styles.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
</head>
<body >
    <a href="#main-content" class="skip-nav">Zum Hauptinhalt springen</a>
    <div id="app" class="mesh-box">
        <!-- Header -->
        <header class="header" role="banner">
            <div class="header-content">
                <div class="logo-container">
                    <img src="../assets/dak-logo.png" alt="DAK-Gesundheit Logo - Zur Startseite">
                </div>
                <div class="header-text">
                    <h1 data-text="DAK Service Portal">DAK Service Portal</h1>
                </div>
            </div>
        </header>

        <!-- Services Grid -->
        <main class="main-layout" id="main-content" role="main">
            <section class="services-grid" aria-label="Service-Kategorien">
                <section class="service-group" aria-labelledby="it-services-heading">
                    <div class="service-group-header">
                        <div class="service-group-title">
                            <svg class="service-group-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><path fill="currentColor" d="M3.135 6.89c.933-.725 1.707-.225 2.74.971c.116.135.272-.023.361-.1S7.687 6.456 7.754 6.4c.066-.059.146-.169.041-.292a36 36 0 0 1-.743-.951c-1.808-2.365 4.946-3.969 3.909-3.994c-.528-.014-2.646-.039-2.963-.004c-1.283.135-2.894 1.334-3.705 1.893c-1.061.726-1.457 1.152-1.522 1.211c-.3.262-.048.867-.592 1.344c-.575.503-.934.122-1.267.414c-.165.146-.627.492-.759.607c-.133.117-.157.314-.021.471c0 0 1.264 1.396 1.37 1.52c.105.122.391.228.567.071c.177-.156.632-.553.708-.623c.078-.066-.05-.861.358-1.177m5.708.517c-.12-.139-.269-.143-.397-.029L7.012 8.63a.29.29 0 0 0-.027.4l8.294 9.439c.194.223.53.246.751.053l.97-.813a.54.54 0 0 0 .052-.758zM19.902 3.39c-.074-.494-.33-.391-.463-.182c-.133.211-.721 1.102-.963 1.506c-.24.4-.832 1.191-1.934.41c-1.148-.811-.749-1.377-.549-1.758c.201-.383.818-1.457.907-1.59c.089-.135-.015-.527-.371-.363c-.357.164-2.523 1.025-2.823 2.26c-.307 1.256.257 2.379-.85 3.494l-1.343 1.4l1.349 1.566l1.654-1.57c.394-.396 1.236-.781 1.998-.607c1.633.369 2.524-.244 3.061-1.258c.482-.906.402-2.814.327-3.308M2.739 17.053a.54.54 0 0 0 0 .758l.951.93c.208.209.538.121.746-.088l4.907-4.824l-1.503-1.714z"/></svg>
                            <h2 id="it-services-heading">IT-Service/Technik</h2>
                        </div>
                    </div>
                    <div class="services-row first-row" role="group" aria-label="IT-Service und Technik Services">
                        <service-card
                            v-for="service in firstRowServices"
                            :key="service.id"
                            :service="service"
                            @click="handleServiceClick"
                        ></service-card>
                    </div>
                </section>
                
                <section class="service-group" aria-labelledby="org-services-heading">
                    <div class="service-group-header">
                        <div class="service-group-title">
                            <svg class="service-group-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" aria-hidden="true">
                                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                                <polyline points="9,22 9,12 15,12 15,22"/>
                            </svg>
                            <h2 id="org-services-heading">Organisation & Arbeitsumfeld</h2>
                        </div>
                    </div>
                    <div class="services-row second-row" role="group" aria-label="Organisation und Arbeitsumfeld Services">
                        <service-card
                            v-for="service in secondRowServices"
                            :key="service.id"
                            :service="service"
                            @click="handleServiceClick"
                        ></service-card>
                    </div>
                </section>
            </section>
            <aside class="website-preview" role="complementary" aria-labelledby="website-preview-heading">
                <div class="preview-header-new">
                    <h3 id="website-preview-heading" class="preview-title-new">DAK-Gesundheit Website</h3>
                    <a class="preview-link-new" href="https://www.dak.de/" target="_blank" rel="noopener noreferrer" aria-label="DAK-Gesundheit Website in neuem Tab öffnen">
                        <span class="material-symbols-outlined">open_in_new</span>
                    </a>
                </div>
                <div class="preview-content-new">
                    <div class="preview-brand">
                        <h4 class="brand-title">DAK-Gesundheit</h4>
                    </div>
                    <p class="preview-description">
                        Besuchen Sie unsere Hauptwebsite für umfassende Gesundheitsinformationen, Services und Mitgliederbereich.
                    </p>
                    <div class="feature-tags">
                        <span class="feature-tag">Mitgliederbereich</span>
                        <span class="feature-tag">Gesundheitsinfos</span>
                    </div>
                    <div class="feature-list">
                        <span class="feature-item"><span class="material-symbols-outlined">check</span>Online-Services</span>
                        <span class="feature-item"><span class="material-symbols-outlined">check</span>Arztsuche</span>
                    </div>
                </div>
                <button class="preview-button-new">
                    Website besuchen
                    <span class="material-symbols-outlined">arrow_forward</span>
                </button>
            </aside>
        </main>


    </div>

    <script src="../scripts/main.js"></script>
</body>
</html>