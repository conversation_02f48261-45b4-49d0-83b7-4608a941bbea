<!DOCTYPE html>
<html lang="de-DE">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IT-Anforderung - DAK Service Portal</title>
    <link rel="stylesheet" href="../styles/styles.css">
    <link rel="stylesheet" href="../styles/it-request.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
</head>
<body>
    <a href="#main-content" class="skip-nav">Zum Hauptinhalt springen</a>
    <div id="app">
        <!-- Main Content -->
        <main class="main-content" id="main-content" role="main">
            <div class="form-container">
                <section class="form-card" aria-labelledby="form-heading">
                    <header class="form-header">
                        <div class="form-header-top">
                            <a href="index.html" class="btn btn-secondary" aria-label="Zurück zur Startseite">
                                Zurück
                            </a>
                            <h1 id="form-heading">IT-Anforderung erstellen</h1>
                        </div>
                        <p>Füllen Sie das Formular aus, um eine neue IT-Anforderung zu stellen</p>
                    </header>

                    <form class="it-request-form" @submit.prevent="submitITRequest" novalidate aria-describedby="form-description">
                        <div id="form-description" class="sr-only">Formular zur Erstellung einer IT-Anforderung mit Pflichtfeldern und optionalen Angaben</div>
                        <div class="form-columns">
                            <!-- Left Column -->
                            <div class="form-column" role="group" aria-labelledby="basic-info-heading">
                                <h2 id="basic-info-heading" class="sr-only">Grundlegende Informationen</h2>
                                <div class="form-group">
                                    <label class="form-label" for="titel">Titel *</label>
                                    <input 
                                        id="titel"
                                        v-model="itRequestForm.titel" 
                                        type="text" 
                                        class="form-input" 
                                        required
                                        aria-required="true"
                                        aria-describedby="titel-help"
                                        placeholder="Kurzer Titel der Anforderung"
                                    />
                                    <div id="titel-help" class="sr-only">Geben Sie einen kurzen, aussagekräftigen Titel für Ihre IT-Anforderung ein</div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label" for="kontakt">Kontakt (E-Mail) *</label>
                                    <input 
                                        id="kontakt"
                                        v-model="itRequestForm.kontakt" 
                                        type="email" 
                                        class="form-input" 
                                        required
                                        aria-required="true"
                                        aria-describedby="kontakt-help"
                                        placeholder="<EMAIL>"
                                    />
                                    <div id="kontakt-help" class="sr-only">Geben Sie Ihre E-Mail-Adresse für Rückfragen ein</div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label" for="originalRequester">Kontaktdaten der ursprünglich anfordernden Person</label>
                                    <input 
                                        id="originalRequester"
                                        v-model="itRequestForm.originalRequester" 
                                        type="text" 
                                        class="form-input"
                                        aria-describedby="original-requester-help"
                                        placeholder="Name und E-Mail des/r ursprünglichen Anforderers"
                                    />
                                    <div id="original-requester-help" class="sr-only">Optionale Angabe, falls Sie die Anforderung für eine andere Person stellen</div>
                                </div>

                                <fieldset class="form-group">
                                    <label class="form-label">Art der Anforderung *</label>
                                    <div class="radio-group" role="radiogroup" aria-required="true" aria-describedby="art-help">
                                        <label class="radio-label">
                                            <input 
                                                type="radio" 
                                                name="artDerAnforderung" 
                                                value="Neue Anwendung"
                                                v-model="itRequestForm.artDerAnforderung"
                                                required
                                                aria-describedby="neue-anwendung-desc"
                                            />
                                            <span>Neue Anwendung</span>
                                        </label>
                                        <label class="radio-label">
                                            <input 
                                                type="radio" 
                                                name="artDerAnforderung" 
                                                value="An bestehende Anwendung"
                                                v-model="itRequestForm.artDerAnforderung"
                                                required
                                                aria-describedby="bestehende-anwendung-desc"
                                            />
                                            <span>An bestehende Anwendung</span>
                                        </label>
                                        <label class="radio-label">
                                            <input 
                                                type="radio" 
                                                name="artDerAnforderung" 
                                                value="Sonstige"
                                                v-model="itRequestForm.artDerAnforderung"
                                                required
                                                aria-describedby="sonstige-desc"
                                            />
                                            <span>Sonstige</span>
                                        </label>
                                    </div>
                                    <div id="art-help" class="sr-only">Wählen Sie die Art Ihrer IT-Anforderung aus</div>
                                </fieldset>

                                <div class="form-group">
                                    <label class="form-label" for="nutzen">Nutzen *</label>
                                    <div class="nutzen-container">
                                        <select 
                                            id="nutzen"
                                            v-model="itRequestForm.nutzen" 
                                            class="form-input" 
                                            required
                                            aria-required="true"
                                            aria-describedby="nutzen-help"
                                        >
                                            <option value="">Bitte wählen...</option>
                                            <option v-for="option in nutzenOptions" :key="option" :value="option">
                                                {{ option }}
                                            </option>
                                        </select>
                                        <div id="nutzen-help" class="sr-only">Wählen Sie den erwarteten Nutzen Ihrer IT-Anforderung aus</div>
                                        <div v-show="itRequestForm.nutzen === 'Gesetzlich'" class="gesetzlich-datum">
                                            <label class="form-label" for="gesetzlichDatum">Gesetzliche Fälligkeit *</label>
                                            <input 
                                                id="gesetzlichDatum"
                                                v-model="itRequestForm.gesetzlichDatum" 
                                                type="date" 
                                                class="form-input datepicker-german"
                                                aria-describedby="faelligkeit-help"
                                                :required="itRequestForm.nutzen === 'Gesetzlich'"
                                                :aria-required="itRequestForm.nutzen === 'Gesetzlich'"
                                            />
                                            <div id="faelligkeit-help" class="sr-only">Geben Sie das Datum der gesetzlichen Fälligkeit an</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column -->
                            <div class="form-column" role="group" aria-labelledby="detailed-info-heading">
                                <h2 id="detailed-info-heading" class="sr-only">Detaillierte Beschreibung</h2>
                                <div class="form-group">
                                    <div class="label-with-info">
                                        <label class="form-label" for="ursache">Ursache</label>
                                        <div class="info-icon-container">
                                            <button 
                                                type="button"
                                                class="info-icon" 
                                                @mouseenter="showTooltip.ursache = true"
                                                @mouseleave="showTooltip.ursache = false"
                                                @focus="showTooltip.ursache = true"
                                                @blur="showTooltip.ursache = false"
                                                aria-describedby="ursache-tooltip"
                                                aria-label="Hilfe zu Ursache anzeigen"
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                                                    <circle cx="12" cy="12" r="10"/>
                                                    <path d="M12 16v-4"/>
                                                    <path d="M12 8h.01"/>
                                                </svg>
                                            </button>
                                            <div id="ursache-tooltip" class="tooltip" v-show="showTooltip.ursache" role="tooltip">
                                                Beschreiben Sie die Ursache oder den Auslöser für diese IT-Anforderung.
                                            </div>
                                        </div>
                                    </div>
                                    <textarea 
                                        id="ursache"
                                        v-model="itRequestForm.ursache" 
                                        class="form-input form-textarea"
                                        placeholder="Beschreibung der Ursache"
                                        rows="4"
                                        aria-describedby="ursache-help"
                                    ></textarea>
                                    <div id="ursache-help" class="sr-only">Optionale Angabe zur Ursache der Anforderung</div>
                                </div>

                                <div class="form-group">
                                    <div class="label-with-info">
                                        <label class="form-label" for="istSituation">Ausgangssituation (Ist) *</label>
                                        <div class="info-icon-container">
                                            <button 
                                                type="button"
                                                class="info-icon" 
                                                @mouseenter="showTooltip.istSituation = true"
                                                @mouseleave="showTooltip.istSituation = false"
                                                @focus="showTooltip.istSituation = true"
                                                @blur="showTooltip.istSituation = false"
                                                aria-describedby="ist-tooltip"
                                                aria-label="Hilfe zur Ausgangssituation anzeigen"
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                                                    <circle cx="12" cy="12" r="10"/>
                                                    <path d="M12 16v-4"/>
                                                    <path d="M12 8h.01"/>
                                                </svg>
                                            </button>
                                            <div id="ist-tooltip" class="tooltip" v-show="showTooltip.istSituation" role="tooltip">
                                                Beschreiben Sie die aktuelle Situation und den bestehenden Prozess detailliert.
                                            </div>
                                        </div>
                                    </div>
                                    <textarea 
                                        id="istSituation"
                                        v-model="itRequestForm.istSituation" 
                                        class="form-input form-textarea" 
                                        required
                                        aria-required="true"
                                        aria-describedby="ist-help"
                                        placeholder="Beschreibung der aktuellen Situation"
                                        rows="6"
                                    ></textarea>
                                    <div id="ist-help" class="sr-only">Pflichtfeld: Beschreiben Sie die aktuelle Ausgangssituation</div>
                                </div>

                                <div class="form-group">
                                    <div class="label-with-info">
                                        <label class="form-label" for="sollSituation">Begründung und Zielstellung (Soll) *</label>
                                        <div class="info-icon-container">
                                            <button 
                                                type="button"
                                                class="info-icon" 
                                                @mouseenter="showTooltip.sollSituation = true"
                                                @mouseleave="showTooltip.sollSituation = false"
                                                @focus="showTooltip.sollSituation = true"
                                                @blur="showTooltip.sollSituation = false"
                                                aria-describedby="soll-tooltip"
                                                aria-label="Hilfe zur Zielstellung anzeigen"
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                                                    <circle cx="12" cy="12" r="10"/>
                                                    <path d="M12 16v-4"/>
                                                    <path d="M12 8h.01"/>
                                                </svg>
                                            </button>
                                            <div id="soll-tooltip" class="tooltip" v-show="showTooltip.sollSituation" role="tooltip">
                                                Beschreiben Sie die gewünschte Zielstellung und begründen Sie die Notwendigkeit der Änderung.
                                            </div>
                                        </div>
                                    </div>
                                    <textarea 
                                        id="sollSituation"
                                        v-model="itRequestForm.sollSituation" 
                                        class="form-input form-textarea" 
                                        required
                                        aria-required="true"
                                        aria-describedby="soll-help"
                                        placeholder="Beschreibung der gewünschten Situation"
                                        rows="6"
                                    ></textarea>
                                    <div id="soll-help" class="sr-only">Pflichtfeld: Beschreiben Sie die gewünschte Zielstellung</div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="form-actions" role="group" aria-label="Formular-Aktionen">
                            <a href="index.html" class="btn btn-secondary" aria-label="Formular abbrechen und zur Startseite zurückkehren">Abbrechen</a>
                            <button type="submit" class="btn btn-primary" aria-describedby="submit-help">IT-Anforderung senden</button>
                            <div id="submit-help" class="sr-only">Sendet die IT-Anforderung per E-Mail an das IT-Support-Team</div>
                        </div>
                    </form>
                </section>
            </div>
        </main>
    </div>

    <script src="../scripts/it-request.js"></script>
</body>
</html>