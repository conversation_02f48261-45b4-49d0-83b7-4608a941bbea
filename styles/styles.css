
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden ;
    margin: 0;
    padding: 20px;
    box-sizing: border-box;
    position: relative;
    background-color: #221a3f;
    background-image:
            linear-gradient(to right, #221a3f, #26538e),
            url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h4v-9H0v-1h4v-9H0v-1h4v-9H0v-1h4v-9H0v-1h4v-9H0v-1h4v-9H0v-1h4v-9H0v-1h4v-9H0v-1h4v-9H0v-1h5v1h9v-1h1v1h9v-1h1v1h9v-1h1v1h9v-1h1v1h9v-1h1v1h9v-1h1v1h9v-1h1v1h9v-1h1v1h9v-1h1v-1h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm-9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm-9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm-9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm-9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9z'/%3E%3Cpath d='M6 5V0h1v5h9V0h1v5h9V0h1v5h9V0h1v5h9V0h1v5h9V0h1v5h9V0h1v5h9V0h1v5h9V0h1v5h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h-1v-9h-9v9h-1v-9h-9v9h-1v-9h-9v9h-1v-9h-9v9h-1v-9h-9v9h-1v-9h-9v9h-1v-9h-9v9h-1v-9h-9v9H0v-1h4v-9H0v-1h4v-9H0v-1h4v-9H0v-1h4v-9H0v-1h4v-9H0v-1h4v-9H0v-1h4v-9H0v-1h4V0h1v5h9V0h1v5h9V0h1v5h9V0h1v5h9V0h1v5h9V0h1v5h9V0h1v5h9V0h1v5h9V0h1v4h1v1h-1v9h1v1h-1v9h1v1h-1v9h1v1h-1v9h1v1h-1v9h1v1h-1v9h1v1h-1v9h1v1h-1v9h1v1h-1v9h1v1h-1v9h-1v-9h-9v9h-1v-9h-9v9h-1v-9h-9v9h-1v-9h-9v9h-1v-9h-9v9h-1v-9h-9v9h-1v-9h-9v9h-1v-9h-9v9h-1v-9h-9v9H5v-1h-4v-9h4v-1h-4v-9h4v-1h-4v-9h4v-1h-4v-9h4v-1h-4v-9h4v-1h-4v-9h4v-1h-4v-9h4v-1h-4v-9h4v-1H5z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-blend-mode: overlay;
}

.container {
    min-height: 100vh;
    position: relative;
    background: 
        radial-gradient(circle at 20% 80%, rgba(204, 102, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(204, 102, 0, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        linear-gradient(135deg, #CC6600 20%, #ffffff 80%);
    padding: 2rem;
}

.header {
    position: relative;
    z-index: 10;
    margin-bottom: 1rem;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 2rem;
}

.logo-container {
    flex-shrink: 0;
    margin-right: 1rem;
    position: relative;
}

.logo-container img {
    height: 60px;
    display: block;
    border-radius: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(5px);
    position: relative;
}

.header-text {
    text-align: left;
}

.header h1 {
    font-size: 1.95rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    color: #ffffff;
    position: relative;
}

.header p {
    font-size: 1rem;
    color: #555;
    text-shadow: 
        0 1px 0 rgba(255, 255, 255, 0.2),
        0 2px 4px rgba(0, 0, 0, 0.1);
    font-weight: 500;
}
.main-layout {
    position: relative;
    z-index: 10;
    display: grid;
    grid-template-columns: 79% 21%;
    gap: 1rem;
    width: 100%;
    height: calc(100vh - 140px);
    padding: 0 25px;
    overflow: hidden;
}

.website-preview {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(16px);
    border-radius: 1rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    position: sticky;
    top: 2rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.website-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.website-preview:hover::before {
    opacity: 1;
}

.website-preview::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 20%, rgba(239, 125, 0, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.website-preview:hover {
    transform: translateY(-1px);
}

.preview-header {
    background: linear-gradient(135deg, #797979 0%, #1c1c1c 100%);
    color: white;
    padding: 0.75rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 
        0 2px 8px rgba(204, 102, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.preview-header h3 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.preview-link {
    color: white;
    text-decoration: none;
    width: 1.25rem;
    height: 1.25rem;
    transition: opacity 0.3s ease;
}

.preview-link:hover {
    opacity: 0.8;
}

.preview-link svg {
    width: 100%;
    height: 100%;
}

.website-preview-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

.preview-image {
    position: relative;
    flex: 1;
    overflow: hidden;
    border-radius: 0.75rem;
    margin: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 
        0 4px 12px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.preview-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(217, 217, 217, 0.95) 0%, rgba(50, 50, 49, 0.95) 100%);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
    transition: all 0.3s ease;
    padding: 1rem;
    border-radius: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.preview-overlay-content {
    text-align: center;
    color: white;
}

.preview-overlay-content h4 {
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

.preview-overlay-content p {
    font-size: 0.8rem;
    line-height: 1.4;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

.preview-features {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.4rem;
    font-size: 0.7rem;
}

.feature {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.4rem;
    background: rgba(255, 255, 255, 0.25);
    border-radius: 0.3rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
    font-weight: 600;
}

.preview-actions {
    padding: 0.75rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.preview-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.4rem;
    width: 100%;
    padding: 0.6rem 1rem;
    background: linear-gradient(135deg, #7c7b7b 0%, #353533 100%);
    color: white;
    text-decoration: none;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    box-shadow: 
        0 4px 12px rgba(204, 102, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.preview-button:hover {
    background: linear-gradient(135deg, #6e6e6c 0%, #201e1b 100%);
    transform: translateY(-2px);
    box-shadow: 
        0 6px 16px rgba(204, 102, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.preview-button svg {
    width: 1rem;
    height: 1rem;
}

/* New Website Preview Styles */
.preview-header-new {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.preview-title-new {
    font-size: 1.25rem;
    font-weight: 600;
    color: white;
    margin: 0;
}

.preview-link-new {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.preview-link-new:hover {
    color: white;
}

.preview-link-new .material-symbols-outlined {
    font-size: 1.5rem;
}

.preview-content-new {
    background: rgba(0, 0, 0, 0.2);
    padding: 1.5rem;
    border-radius: 0.5rem;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    margin-bottom: 1.5rem;
}

.preview-brand {
    margin-bottom: 1rem;
}

.brand-title {
    font-size: 2rem;
    font-weight: 700;
    color: #e83274;
    margin: 0;
}

.preview-description {
    color: #d1d5db;
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
}

.feature-tags {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.feature-tag {
    background: rgba(34, 197, 94, 0.2);
    color: #86efac;
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    border: 1px solid rgba(34, 197, 94, 0.5);
}

.feature-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #d1d5db;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.feature-item .material-symbols-outlined {
    font-size: 1rem;
    color: #4ade80;
}

.preview-button-new {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem;
    border-radius: 0.5rem;
    color: white;
    font-weight: 600;
    background-color: #8e0c4f;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    gap: 0.5rem;
}

.preview-button-new:hover {
    background-color: #e83274;
}

.preview-button-new .material-symbols-outlined {
    font-size: 1.25rem;
}

.services-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 0 0.75rem;
    align-content: start;
    height: 100%;
    overflow: hidden;
}

/* Service Group Container */
.service-group {
    padding: 1.5rem;
    border-radius: 2px;
    margin-top:30px;
    position: relative;
    overflow: auto;
}



/* Service Group Header */
.service-group-header {
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.service-group-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.service-group-icon {
    width: 1.8rem;
    height: 1.8rem;
    color: #ed7c09;
    filter: drop-shadow(0 2px 4px rgba(204, 102, 0, 0.3));
}

.service-group-title h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.9);
    text-shadow:
        0 1px 0 rgba(2, 2, 4, 0.2),
        0 1px 1px rgba(3, 0, 0, 0.8);
    margin: 5px;
}

.services-row {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 0.75rem;
}

.service-card {
    border-radius: 1rem;
    padding: 0.75rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    height: 100%;
    background: rgba(250, 250, 252, 0.12);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    min-height: 280px;
    min-width: 200px;
    display: flex;
    flex-direction: column;
}

.service-card:hover::before {
    opacity: 1;
}


.service-card:hover {
    transform: translateY(-8px) scale(1.03);
    background: rgba(255, 255, 255, 1);
    border-color: rgba(239, 125, 0, 0.6);
    box-shadow: 
        0 20px 50px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(239, 125, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.65),
        0 0 30px rgba(239, 125, 0, 0.15);
}

.service-card:active {
    transform: translateY(-4px) scale(1.01);
    transition: all 0.1s ease;
}

.service-card--inverted {
    background: linear-gradient(135deg, #8e0c4f 100%, #e83274 100%);
    color: white;
}

.service-card--inverted .service-header h3 {
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
    color: #333;
}

.service-card--inverted .service-header h3 {
    color: white;
}

.service-card--inverted .service-description {
    color: white;
}

.service-card--inverted .service-icon {
    background: white;
}

.service-card--inverted .service-icon .icon {
    color: #EF7D00;
}

.service-card--inverted .service-footer span {
    color: white;
}

.service-card--inverted .external-link {
    color: white;
}

.service-card--inverted:hover {
    transform: translateY(-8px) scale(1.03);
    background: rgba(255, 255, 255, 1);
    border-color: rgba(239, 125, 0, 0.6);
    box-shadow:
        0 20px 50px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(239, 125, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.65),
        0 0 30px rgba(239, 125, 0, 0.15);
    color: #333;
}

.service-card--inverted:hover .service-header h3 {
    color: #333;
}

.service-card--inverted:hover .service-description {
    color: #070808;
}

.service-card--inverted:hover .service-icon {
    background: linear-gradient(135deg, #ff6b00 0%, #EF7D00 100%);
    transform: scale(1.1) rotate(5deg);
    box-shadow:
        0 6px 16px rgba(239, 125, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.service-card--inverted:hover .service-icon .icon {
    color: white;
}

.service-card--inverted:hover .service-footer span {
    color: #EF7D00;
    transform: translateX(2px);
}

.service-card--inverted:hover .external-link {
    color: #EF7D00;
    transform: translateX(3px) translateY(-1px) rotate(5deg);
}

.service-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.service-icon {
    width: 2rem;
    height: 2rem;
    background: linear-gradient(135deg, #EF7D00 0%, #ff6b00 100%);
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 
        0 4px 12px rgba(239, 125, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.service-card:hover .service-icon {
    background: linear-gradient(135deg, #ff6b00 0%, #EF7D00 100%);
    transform: scale(1.1) rotate(5deg);
    box-shadow: 
        0 6px 16px rgba(239, 125, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.service-icon .icon {
    width: 1.2rem;
    height: 1.2rem;
    color: white;
    transition: all 0.3s ease;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.service-description {
    color: #ffffff;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    text-shadow: none;
    line-height: 1.3;
}

.service-card:hover .service-description {
    color: #060000;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    text-shadow: none;
    line-height: 1.3;
}

.service-image {
    position: relative;
    width: 100%;
    height: 100px;
    border-radius: 0.75rem;
    overflow: hidden;
    margin-bottom: 0.75rem;
    flex-shrink: 0;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 
        0 4px 12px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-card:hover .service-image img {
    transform: scale(1.05);
}

.service-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(239, 125, 0, 0.95) 0%, rgba(255, 107, 0, 0.95) 100%);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem;
    opacity: 0;
    transition: all 0.3s ease;
    border-radius: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.service-card:hover .service-overlay {
    opacity: 1;
}

.service-title{
    font-size: 0.85rem;
    font-weight: 700;
    margin: 0;
    color: #fefefe;
    transition: all 0.3s ease;
}

.service-card:hover .service-title{
    font-size: 0.85rem;
    font-weight: 700;
    margin: 0;
    color: #010101;
    transition: all 0.3s ease;
}

.service-overlay p {
    color: white;
    font-size: 0.7rem;
    text-align: center;
    font-weight: 600;
    line-height: 1.3;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.service-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: auto;
    padding-top: 0.75rem;
    border-top: 1px solid rgba(252, 252, 252, 0.1);
}

.service-footer span {
    color: #fafafb;
    font-size: 0.75rem;
    font-weight: 600;
    transition: all 0.3s ease;
    text-shadow: none;
}

.service-card:hover .service-footer span {
    color: #EF7D00;
    transform: translateX(2px);
}

.external-link {
    width: 0.9rem;
    height: 0.9rem;
    color: #fbfafa;
    transition: all 0.3s ease;
    filter: none;
}

.service-card:hover .external-link {
    color: #EF7D00;
    transform: translateX(3px) translateY(-1px) rotate(5deg);
}

.news-section {
    position: relative;
    z-index: 10;
    margin-top: 4rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.news-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 1rem;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.news-content h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
}

.news-content p {
    color: #666;
    margin-bottom: 2rem;
}

.news-placeholder {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
}

.placeholder-line {
    height: 1rem;
    background: rgba(239, 125, 0, 0.2);
    border-radius: 0.5rem;
    width: 100%;
}

.placeholder-line.short {
    width: 75%;
}

.placeholder-line.shorter {
    width: 50%;
}

@media (max-width: 1200px) {
    .services-row {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 1000px) {
    .services-row {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    body {
        margin: 10px;
    }

    .header-content {
        padding: 0.75rem 1rem;
        gap: 1rem;
    }

    .logo-container img {
        height: 35px;
    }

.header h1 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    color: rgba(239, 125, 0, 0.9);
    position: relative;
}

    .header p {
        font-size: 0.875rem;
    }

    .main-layout {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .website-preview {
        display: none;
    }

    .services-row {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .services-grid {
        padding: 0 0.5rem;
    }

    .service-card {
        padding: 0.75rem;
    }

    .service-image {
        height: 120px;
    }

    .service-header h3 {
        font-size: 0.85rem;
    }

    .service-description {
        font-size: 0.7rem;
    }
}

/* Focus and Keyboard Navigation Styles */
*:focus {
    outline: 3px solid #CC6600;
    outline-offset: 2px;
}

.service-card:focus {
    outline: 3px solid #CC6600;
    outline-offset: 2px;
    transform: translateY(-4px) scale(1.02);
    background: rgba(255, 255, 255, 0.7);
    border-color: rgba(204, 102, 0, 0.6);
    box-shadow: 
        0 12px 32px rgba(0, 0, 0, 0.12),
        0 0 0 1px rgba(204, 102, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.65),
        0 0 20px rgba(204, 102, 0, 0.15);
}

.preview-link:focus,
.preview-button:focus {
    outline: 3px solid #ffffff;
    outline-offset: 2px;
}

/* Skip Navigation Link */
.skip-nav {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #CC6600;
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    font-weight: 600;
    transition: top 0.3s;
}

.skip-nav:focus {
    top: 6px;
    outline: 3px solid #ffffff;
    outline-offset: 2px;
}

/* Dialog Styles */
.dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.dialog-overlay.show {
    opacity: 1;
    visibility: visible;
}

.dialog {
    background: #F0F2F7;
    border-radius: 1rem;
    padding: 2rem;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.dialog-overlay.show .dialog {
    transform: scale(1);
}

.dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e5e5e5;
}

.dialog-title {
    font-size: 1.75rem;
    font-weight: 700;
    background: linear-gradient(135deg, #EF7D00 0%, #ff6b00 50%, #d97000 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    text-shadow: 0 2px 4px rgba(239, 125, 0, 0.2);
}

.dialog-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: background-color 0.2s ease;
}

.dialog-close:hover {
    background: linear-gradient(135deg, rgba(239, 125, 0, 0.2) 0%, rgba(255, 107, 0, 0.2) 100%);
    border-color: rgba(239, 125, 0, 0.4);
    transform: scale(1.1) rotate(90deg);
    box-shadow: 0 4px 8px rgba(239, 125, 0, 0.3);
}

.dialog-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    position: relative;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 0.75rem;
    padding: 1.25rem;
    margin-bottom: 0.5rem;
    border: 1px solid rgba(239, 125, 0, 0.1);
    transition: all 0.3s ease;
}

.form-group:hover {
    background: rgba(255, 255, 255, 0.8);
    border-color: rgba(239, 125, 0, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(239, 125, 0, 0.1);
}

.form-label {
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.75rem;
    font-size: 0.95rem;
    background: linear-gradient(135deg, #EF7D00 0%, #d97000 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.form-label::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, #EF7D00, transparent);
    border-radius: 1px;
}

.form-input {
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-input:focus {
    outline: none;
    border-color: #EF7D00;
    box-shadow: 0 0 0 3px rgba(239, 125, 0, 0.1);
}

.form-textarea {
    min-height: 100px;
    resize: vertical;
    font-family: inherit;
}

.dialog-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e5e5;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #EF7D00 0%, #ff6b00 50%, #EF7D00 100%);
    background-size: 200% 100%;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 15px rgba(239, 125, 0, 0.3);
    position: relative;
    overflow: hidden;
    animation: gradientMove 3s ease-in-out infinite;
}

@keyframes gradientMove {
    0%, 100% {
        background-position: 0% 0%;
    }
    50% {
        background-position: 100% 0%;
    }
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(239, 125, 0, 0.4);
    animation-duration: 1s;
}

.btn-secondary {
    background: linear-gradient(135deg, rgba(245, 245, 245, 0.9) 0%, rgba(229, 229, 229, 0.9) 100%);
    color: #4a5568;
    border: 1px solid rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, rgba(229, 229, 229, 0.9) 0%, rgba(209, 209, 209, 0.9) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Radio Button Styles */
.radio-group {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: #333;
}

.radio-label input[type="radio"] {
    margin: 0;
    width: 1rem;
    height: 1rem;
    accent-color: #EF7D00;
}

.radio-label span {
    white-space: nowrap;
}

/* Label with Info Icon Styles */
.label-with-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.info-icon-container {
    position: relative;
    display: inline-block;
}

.info-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.2rem;
    height: 1.2rem;
    color: #EF7D00;
    cursor: pointer;
    border-radius: 50%;
    background: rgba(239, 125, 0, 0.1);
    transition: all 0.2s ease;
}

.info-icon:hover {
    background: rgba(239, 125, 0, 0.2);
    transform: scale(1.1);
}

.info-icon svg {
    width: 0.8rem;
    height: 0.8rem;
}

.tooltip {
    position: absolute;
    top: 50%;
    left: calc(100% + 0.75rem);
    transform: translateY(-50%);
    background: #333;
    color: white;
    padding: 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.7rem;
    line-height: 1.4;
    width: 200px;
    z-index: 1001;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    white-space: normal;
}

.tooltip::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -0.25rem;
    transform: translateY(-50%);
    border-top: 0.25rem solid transparent;
    border-bottom: 0.25rem solid transparent;
    border-right: 0.25rem solid #333;
}

.hover-info-text {
    font-size: 0.7rem;
    color: #666;
    line-height: 1.4;
    margin-top: 0.5rem;
}

/* Nutzen Container Styles */
.nutzen-container {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.nutzen-container select {
    flex: 1;
    margin-bottom: 0;
}

.datepicker-inline {
    flex: 0 0 auto;
    min-width: 150px;
    margin-bottom: 0;
}

.datepicker-german::-webkit-calendar-picker-indicator {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"><path fill="%23666" d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/></svg>');
}

.datepicker-german {
    position: relative;
}

@media (max-width: 768px) {
    .nutzen-container {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .datepicker-inline {
        min-width: auto;
        width: 100%;
    }
    
    .radio-group {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .tooltip {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-width: 380px;
        font-size: 0.8rem;
    }
    
    .tooltip::before {
        display: none;
    }
}

@media (max-width: 480px) {
    .header-text {
        display: none;
    }

    .header-content {
        justify-content: center;
    }

    .main-layout {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .services-grid {
        padding: 0 0.25rem;
    }

    .news-content {
        padding: 1.5rem;
    }

    .dialog {
        padding: 1.5rem;
        width: 95%;
    }

    .dialog-actions {
        flex-direction: column;
    }

    .btn {
        width: 100%;
    }
}
.mesh-box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    background-image:
            radial-gradient(circle at center, rgba(95, 91, 91, 0.3) 1px, transparent 3px),
            radial-gradient(circle at center, rgba(95, 91, 91, 0.3) 1px, transparent 3px);
    background-size: 105px 91px, 105px 91px;
    background-position: 0 0, 52.5px 45.5px;
}