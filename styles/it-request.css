/* IT Request Page Styles */

/* Screen Reader Only Content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Background and Layout */
body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    background: 
        linear-gradient(135deg, rgba(204, 102, 0, 0.3) 0%, rgba(0, 0, 0, 0.4) 100%),
        url('https://images.unsplash.com/photo-1557804506-669a67965ba0?w=1920&h=1080&fit=crop&crop=center') center/cover no-repeat fixed;
    position: relative;
}

body::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: 
        radial-gradient(circle at 25% 25%, rgba(204, 102, 0, 0.1) 0%, transparent 40%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 40%);
    z-index: -1;
    animation: backgroundFloat 15s ease-in-out infinite;
}

@keyframes backgroundFloat {
    0%, 100% {
        opacity: 0.8;
    }
    50% {
        opacity: 0.6;
    }
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 4px 32px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.header-text h1 {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    background: linear-gradient(135deg, #EF7D00 0%, #ff6b00 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-text p {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
}

.back-button {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 0.5rem;
    color: #3f3f3f;
    text-decoration: none;
    font-weight: 700;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
    min-height: 44px;
}

.back-button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.back-button svg {
    width: 0.875rem;
    height: 0.875rem;
}

/* Main Content */
.main-content {
    padding: 1rem;
    max-width: 1000px;
    margin: 0 auto;
}

/* Form Container - Liquid Glass Effect */
.form-container {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    min-height: calc(100vh - 150px);
}

.form-card {
    width: 100%;
    max-width: 900px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 1.5rem;
    padding: 2rem;
    box-shadow: 
        0 12px 40px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.form-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
}

.form-card::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 20%, rgba(239, 125, 0, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Form Header */
.form-header {
    margin-bottom: 2rem;
}

.form-header-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    position: relative;
}

.form-header h1 {
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(135deg, #EF7D00 0%, #ff6b00 50%, #d97000 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    text-shadow: 0 2px 4px rgba(204, 102, 0, 0.2);
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
}

.form-header p {
    font-size: 1rem;
    color: #333;
    margin: 0;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    text-align: center;
}

/* Two Column Layout */
.form-columns {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.form-column {
    display: flex;
    flex-direction: column;
    gap: 0.875rem;
}

/* Form Groups - Enhanced Glass Effect */
.form-group {
    position: relative;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 0.75rem;
    padding: 1rem;
    transition: all 0.3s ease;
    box-shadow: 
        0 4px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.form-group:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(204, 102, 0, 0.4);
    transform: translateY(-1px);
    box-shadow: 
        0 6px 20px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.form-group:focus-within {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(204, 102, 0, 0.5);
    box-shadow: 
        0 8px 24px rgba(204, 102, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Form Labels */
.form-label,
fieldset legend.form-label {
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    background:  #EF7D00;
    background-clip: text;
    position: relative;
    display: block;
}

fieldset {
    border: none;
    padding: 0;
    margin: 0;
}

fieldset legend {
    padding: 0;
}

.form-label::after,
fieldset legend.form-label::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, #EF7D00, transparent);
    border-radius: 1px;
}

/* Form Inputs - Glass Effect */
.form-input {
    width: 100%;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.9);
    border-radius: 0.5rem;
    font-size: 0.9rem;
    color: #333;
    transition: all 0.3s ease;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
    min-height: 44px;
}

.form-input:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.95);
    border-color: rgba(239, 125, 0, 0.6);
    box-shadow: 
        0 0 0 2px rgba(239, 125, 0, 0.15),
        inset 0 2px 4px rgba(0, 0, 0, 0.05);
}

.form-input::placeholder {
    color: rgba(60, 60, 60, 0.9);
}

.form-textarea {
    min-height: 100px;
    resize: vertical;
    font-family: inherit;
    line-height: 1.4;
}

/* Radio Groups */
.radio-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: #333;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 0.4rem;
    transition: all 0.3s ease;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    min-height: 44px;
}

.radio-label:hover {
    background: rgba(255, 255, 255, 0.25);
}

.radio-label input[type="radio"] {
    margin: 0;
    width: 1.2rem;
    height: 1.2rem;
    accent-color: #EF7D00;
}

/* Nutzen Container */
.nutzen-container {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.gesetzlich-datum {
    display: flex;
    flex-direction: column;
    gap: 0.4rem;
    padding: 0.75rem;
    background: rgba(239, 125, 0, 0.15);
    border-radius: 0.5rem;
    border: 1px solid rgba(239, 125, 0, 0.3);
}

/* Info Icons and Tooltips */
.label-with-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.info-icon-container {
    position: relative;
}

.info-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.5rem;
    height: 1.5rem;
    color: #EF7D00;
    cursor: pointer;
    border-radius: 50%;
    background: rgba(204, 102, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(204, 102, 0, 0.2);
    transition: all 0.3s ease;
}

.info-icon:hover {
    background: rgba(204, 102, 0, 0.2);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(204, 102, 0, 0.2);
}

.info-icon svg {
    width: 1rem;
    height: 1rem;
}

.tooltip {
    position: absolute;
    top: 50%;
    left: calc(100% + 1rem);
    transform: translateY(-50%);
    background: rgba(51, 51, 51, 0.95);
    backdrop-filter: blur(20px);
    color: white;
    padding: 1rem;
    border-radius: 0.75rem;
    font-size: 0.8rem;
    line-height: 1.4;
    width: 250px;
    z-index: 1001;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.tooltip::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -0.5rem;
    transform: translateY(-50%);
    border-top: 0.5rem solid transparent;
    border-bottom: 0.5rem solid transparent;
    border-right: 0.5rem solid rgba(51, 51, 51, 0.95);
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.3);
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 140px;
    min-height: 44px;
    backdrop-filter: blur(20px);
}

.btn-primary {
    background: linear-gradient(135deg, #EF7D00 0%, #ff6b00 50%, #EF7D00 100%);
    background-size: 200% 100%;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 
        0 8px 24px rgba(204, 102, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    animation: gradientMove 3s ease-in-out infinite;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 
        0 12px 32px rgba(204, 102, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    animation-duration: 1s;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: #4a5568;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

@keyframes gradientMove {
    0%, 100% {
        background-position: 0% 0%;
    }
    50% {
        background-position: 100% 0%;
    }
}

/* Focus and Keyboard Navigation Styles */
*:focus {
    outline: 3px solid #CC6600;
    outline-offset: 2px;
}

.form-input:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.95);
    border-color: rgba(204, 102, 0, 0.6);
    box-shadow: 
        0 0 0 3px rgba(204, 102, 0, 0.15),
        inset 0 2px 4px rgba(0, 0, 0, 0.05);
}

.back-button:focus,
.btn:focus {
    outline: 3px solid #CC6600;
    outline-offset: 2px;
}

.radio-label:focus-within {
    background: rgba(204, 102, 0, 0.15);
    outline: 2px solid #CC6600;
    outline-offset: 1px;
}

.info-icon:focus {
    outline: 2px solid #CC6600;
    outline-offset: 1px;
    background: rgba(204, 102, 0, 0.2);
    transform: scale(1.1);
}

/* Skip Navigation Link */
.skip-nav {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #CC6600;
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    font-weight: 600;
    transition: top 0.3s;
}

.skip-nav:focus {
    top: 6px;
    outline: 3px solid #ffffff;
    outline-offset: 2px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .form-columns {
        grid-template-columns: 1fr;
        gap: 1.25rem;
    }
    
    .form-card {
        padding: 1.5rem;
    }
    
    .main-content {
        padding: 0.75rem;
    }
}

@media (max-width: 768px) {
    .header-content {
        padding: 0.75rem;
        flex-direction: column;
        gap: 0.75rem;
        text-align: center;
    }

    .header-text h1 {
        font-size: 1.4rem;
    }

    .form-header-top {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .form-header h2 {
        font-size: 1.75rem;
        text-align: center;
        width: 100%;
    }

    .back-button {
        align-self: flex-start;
    }

    .form-card {
        padding: 1.25rem;
        border-radius: 1.25rem;
    }

    .form-actions {
        flex-direction: column;
        gap: 0.25rem;
    }

    .btn {
        width: 100%;
    }

    .tooltip {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-width: 90vw;
        width: auto;
    }

    .tooltip::before {
        display: none;
    }
}

@media (max-width: 480px) {
    .form-group {
        padding: 0.75rem;
    }

    .radio-group {
        gap: 0.5rem;
    }

    .radio-label {
        padding: 0.4rem;
        font-size: 0.85rem;
    }

    .form-header {
        margin-bottom: 1.5rem;
    }

    .form-header h2 {
        font-size: 1.5rem;
    }

    .back-button {
        padding: 0.4rem 0.75rem;
        font-size: 0.8rem;
        gap: 0.25rem;
    }

    .back-button svg {
        width: 0.75rem;
        height: 0.75rem;
    }
}