const ItRequestDialog = {
    props: {
        showDialog: {
            type: Boolean,
            required: true
        }
    },
    data() {
        return {
            itRequestForm: {
                titel: '',
                kontakt: '',
                originalRequester: '',
                artDerAnforderung: '',
                ursache: '',
                istSituation: '',
                sollSituation: '',
                nutzen: '',
                gesetzlichDatum: ''
            },
            nutzenOptions: [
                'Automatisierung',
                'Ergonomie', 
                'Zeitersparnis',
                'Medienbruchloser Workflow',
                'Fehlermeldung',
                'Gesetzlich',
                'Kostenoptimierung',
                'Neues Geschäftsfeld',
                'Optimierung Sachbearbeitung',
                'Revisionssicherheit',
                'RSA-relevant',
                'Steigerung Kundenzufriedenheit'
            ],
            showTooltip: {
                ursache: false,
                istSituation: false,
                sollSituation: false
            }
        };
    },
    template: `
        <div class="dialog-overlay" :class="{ show: showDialog }" @click="closeDialog">
            <div class="dialog" @click.stop>
                <div class="dialog-header">
                    <h2 class="dialog-title">IT-Anforderung</h2>
                    <button class="dialog-close" @click="closeDialog">&times;</button>
                </div>
                <form class="dialog-form" @submit.prevent="submitITRequest">
                    <div class="form-group">
                        <label class="form-label" for="titel">Titel *</label>
                        <input 
                            id="titel"
                            v-model="itRequestForm.titel" 
                            type="text" 
                            class="form-input" 
                            required
                            placeholder="Kurzer Titel der Anforderung"
                        />
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="kontakt">Kontakt (Mail) *</label>
                        <input 
                            id="kontakt"
                            v-model="itRequestForm.kontakt" 
                            type="email" 
                            class="form-input" 
                            required
                            placeholder="<EMAIL>"
                        />
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="originalRequester">Kontaktdaten der ursprünglich anfordernden Person</label>
                        <input 
                            id="originalRequester"
                            v-model="itRequestForm.originalRequester" 
                            type="text" 
                            class="form-input"
                            placeholder="Name und E-Mail des/r ursprünglichen Anforderers"
                        />
                    </div>
                    <div class="form-group">
                        <label class="form-label">Art der Anforderung *</label>
                        <div class="radio-group">
                            <label class="radio-label">
                                <input 
                                    type="radio" 
                                    name="artDerAnforderung" 
                                    value="Neue Anwendung"
                                    v-model="itRequestForm.artDerAnforderung"
                                    required
                                />
                                <span>Neue Anwendung</span>
                            </label>
                            <label class="radio-label">
                                <input 
                                    type="radio" 
                                    name="artDerAnforderung" 
                                    value="An bestehende Anwendung"
                                    v-model="itRequestForm.artDerAnforderung"
                                    required
                                />
                                <span>An bestehende Anwendung</span>
                            </label>
                            <label class="radio-label">
                                <input 
                                    type="radio" 
                                    name="artDerAnforderung" 
                                    value="Sonstige"
                                    v-model="itRequestForm.artDerAnforderung"
                                    required
                                />
                                <span>Sonstige</span>
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="label-with-info">
                            <label class="form-label" for="ursache">Ursache</label>
                            <div class="info-icon-container">
                                <div 
                                    class="info-icon" 
                                    @mouseenter="showTooltip.ursache = true"
                                    @mouseleave="showTooltip.ursache = false"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-info-icon lucide-info"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>
                                </div>
                                <div class="tooltip" v-show="showTooltip.ursache">
                                  Ursache:Lorem Ipsum is simply fake text of the printing and typesetting industry.
                                </div>
                            </div>
                        </div>
                        <textarea 
                            id="ursache"
                            v-model="itRequestForm.ursache" 
                            class="form-input form-textarea"
                            placeholder="Beschreibung der Ursache"
                        ></textarea>
                    </div>
                    <div class="form-group">
                        <div class="label-with-info">
                            <label class="form-label" for="istSituation">Ausgangssituation (Ist) *</label>
                            <div class="info-icon-container">
                                <div 
                                    class="info-icon" 
                                    @mouseenter="showTooltip.istSituation = true"
                                    @mouseleave="showTooltip.istSituation = false"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-info-icon lucide-info"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>
                                </div>
                                <div class="tooltip" v-show="showTooltip.istSituation">
                                   Ausgangssituation: Lorem Ipsum is simply fake text of the printing and typesetting industry.
                                </div>
                            </div>
                        </div>
                        <textarea 
                            id="istSituation"
                            v-model="itRequestForm.istSituation" 
                            class="form-input form-textarea" 
                            required
                            placeholder="Beschreibung der aktuellen Situation"
                        ></textarea>
                    </div>
                    <div class="form-group">
                        <div class="label-with-info">
                            <label class="form-label" for="sollSituation">Begründung und Zielstellung (Soll) *</label>
                            <div class="info-icon-container">
                                <div 
                                    class="info-icon" 
                                    @mouseenter="showTooltip.sollSituation = true"
                                    @mouseleave="showTooltip.sollSituation = false"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-info-icon lucide-info"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>
                                </div>
                                <div class="tooltip" v-show="showTooltip.sollSituation">
                                Begründung und Zielstellung: Lorem Ipsum is simply dummy text of the printing and typesetting industry.
                                </div>
                            </div>
                        </div>
                        <textarea 
                            id="sollSituation"
                            v-model="itRequestForm.sollSituation" 
                            class="form-input form-textarea" 
                            required
                            placeholder="Beschreibung der gewünschten Situation"
                        ></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="nutzen">Nutzen *</label>
                        <div class="nutzen-container">
                            <select 
                                id="nutzen"
                                v-model="itRequestForm.nutzen" 
                                class="form-input" 
                                required
                            >
                                <option value="">Bitte wählen...</option>
                                <option v-for="option in nutzenOptions" :key="option" :value="option">
                                    {{ option }}
                                </option>
                            </select>
                            <label v-show="itRequestForm.nutzen === 'Gesetzlich'" class="form-label" for="gesetzlichDatum">Fälligkeit</label>
                            <input 
                            aria-label="Fälligkeit"
                                v-show="itRequestForm.nutzen === 'Gesetzlich'"
                                id="gesetzlichDatum"
                                v-model="itRequestForm.gesetzlichDatum" 
                                type="date" 
                                class="form-input datepicker-inline datepicker-german"
                                placeholder="TT.MM.JJJJ"
                                :required="itRequestForm.nutzen === 'Gesetzlich'"
                            />
                        </div>
                    </div>
                    <div class="dialog-actions">
                        <button type="button" class="btn btn-secondary" @click="closeDialog">Abbrechen</button>
                        <button type="submit" class="btn btn-primary">Anforderung senden</button>
                    </div>
                </form>
            </div>
        </div>
    `,
    methods: {
        closeDialog() {
            this.$emit('close');
            this.resetForm();
        },

        resetForm() {
            this.itRequestForm = {
                titel: '',
                kontakt: '',
                originalRequester: '',
                artDerAnforderung: '',
                ursache: '',
                istSituation: '',
                sollSituation: '',
                nutzen: '',
                gesetzlichDatum: ''
            };
            this.showTooltip = {
                ursache: false,
                istSituation: false,
                sollSituation: false
            };
        },


        submitITRequest() {
            const formData = {
                titel: this.itRequestForm.titel,
                kontakt: this.itRequestForm.kontakt,
                originalRequester: this.itRequestForm.originalRequester,
                artDerAnforderung: this.itRequestForm.artDerAnforderung,
                ursache: this.itRequestForm.ursache,
                istSituation: this.itRequestForm.istSituation,
                sollSituation: this.itRequestForm.sollSituation,
                nutzen: this.itRequestForm.nutzen,
                gesetzlichDatum: this.itRequestForm.gesetzlichDatum
            };

            // Log all form data when "Anforderung senden" is clicked
            console.log('IT Request Form Data:', formData);

            // Create email body
            const emailBody = `IT-Anforderung: ${formData.titel}
                
Kontakt: ${formData.kontakt}
${formData.originalRequester ? `Original Requester: ${formData.originalRequester}` : ''}

Art der Anforderung: ${formData.artDerAnforderung}
${formData.ursache ? `Ursache: ${formData.ursache}` : ''}
                
Ausgangssituation (Ist):
${formData.istSituation}
                
Begründung und Zielstellung (Soll):
${formData.sollSituation}

Nutzen: ${formData.nutzen}
${formData.nutzen === 'Gesetzlich' && formData.gesetzlichDatum ? `Gesetzliches Datum: ${formData.gesetzlichDatum}` : ''}`;

            // Create mailto link
            const mailtoLink = `mailto:<EMAIL>?subject=IT-Anforderung: ${encodeURIComponent(formData.titel)}&body=${encodeURIComponent(emailBody)}`;
            
            // Open email client
            window.location.href = mailtoLink;
            
            // Close dialog
            this.closeDialog();
        }
    }
};