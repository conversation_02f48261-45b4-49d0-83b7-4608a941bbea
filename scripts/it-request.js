const { createApp } = Vue;

const app = createApp({
    data() {
        return {
            itRequestForm: {
                titel: '',
                kontakt: '',
                originalRequester: '',
                artDerAnforderung: '',
                ursache: '',
                istSituation: '',
                sollSituation: '',
                nutzen: '',
                gesetzlichDatum: ''
            },
            nutzenOptions: [
                'Automatisierung',
                'Ergonomie', 
                'Zeitersparnis',
                'Medienbruchloser Workflow',
                'Fehlermeldung',
                'Gesetzlich',
                'Kostenoptimierung',
                'Neues Geschäftsfeld',
                'Optimierung Sachbearbeitung',
                'Revisionssicherheit',
                'RSA-relevant',
                'Steigerung Kundenzufriedenheit'
            ],
            showTooltip: {
                ursache: false,
                istSituation: false,
                sollSituation: false
            }
        };
    },
    methods: {
        submitITRequest() {
            // Validate required fields
            const requiredFields = [
                { field: 'titel', name: 'Titel' },
                { field: 'kontakt', name: 'Kontakt (E-Mail)' },
                { field: 'artDerAnforderung', name: 'Art der Anforderung' },
                { field: 'istSituation', name: 'Ausgangssituation (Ist)' },
                { field: 'sollSituation', name: 'Begründung und Zielstellung (Soll)' },
                { field: 'nutzen', name: 'Nutzen' }
            ];

            const missingFields = requiredFields.filter(field => 
                !this.itRequestForm[field.field] || this.itRequestForm[field.field].trim() === ''
            );

            if (this.itRequestForm.nutzen === 'Gesetzlich' && !this.itRequestForm.gesetzlichDatum) {
                missingFields.push({ field: 'gesetzlichDatum', name: 'Gesetzliche Fälligkeit' });
            }

            if (missingFields.length > 0) {
                const fieldNames = missingFields.map(field => field.name).join(', ');
                this.showNotification(`Bitte füllen Sie folgende Pflichtfelder aus: ${fieldNames}`, 'error');
                
                // Focus first missing field
                const firstMissingField = document.getElementById(missingFields[0].field);
                if (firstMissingField) {
                    firstMissingField.focus();
                    firstMissingField.setAttribute('aria-invalid', 'true');
                }
                return;
            }

            // Validate email format
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(this.itRequestForm.kontakt)) {
                this.showNotification('Bitte geben Sie eine gültige E-Mail-Adresse ein.', 'error');
                document.getElementById('kontakt').focus();
                document.getElementById('kontakt').setAttribute('aria-invalid', 'true');
                return;
            }

            const formData = {
                titel: this.itRequestForm.titel,
                kontakt: this.itRequestForm.kontakt,
                originalRequester: this.itRequestForm.originalRequester,
                artDerAnforderung: this.itRequestForm.artDerAnforderung,
                ursache: this.itRequestForm.ursache,
                istSituation: this.itRequestForm.istSituation,
                sollSituation: this.itRequestForm.sollSituation,
                nutzen: this.itRequestForm.nutzen,
                gesetzlichDatum: this.itRequestForm.gesetzlichDatum
            };

            console.log('IT Request Form Data:', formData);

            const emailBody = `IT-Anforderung: ${formData.titel}
                
Kontakt: ${formData.kontakt}
${formData.originalRequester ? `Original Requester: ${formData.originalRequester}` : ''}

Art der Anforderung: ${formData.artDerAnforderung}
${formData.ursache ? `Ursache: ${formData.ursache}` : ''}
                
Ausgangssituation (Ist):
${formData.istSituation}
                
Begründung und Zielstellung (Soll):
${formData.sollSituation}

Nutzen: ${formData.nutzen}
${formData.nutzen === 'Gesetzlich' && formData.gesetzlichDatum ? `Gesetzliches Datum: ${formData.gesetzlichDatum}` : ''}`;

            const mailtoLink = `mailto:<EMAIL>?subject=IT-Anforderung: ${encodeURIComponent(formData.titel)}&body=${encodeURIComponent(emailBody)}`;
            
            window.location.href = mailtoLink;
            
            this.showNotification('E-Mail-Client wird geöffnet...', 'success');
            
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 2000);
        },

        resetForm() {
            this.itRequestForm = {
                titel: '',
                kontakt: '',
                originalRequester: '',
                artDerAnforderung: '',
                ursache: '',
                istSituation: '',
                sollSituation: '',
                nutzen: '',
                gesetzlichDatum: ''
            };
            this.showTooltip = {
                ursache: false,
                istSituation: false,
                sollSituation: false
            };
        },
        
        showNotification(message, type = 'info') {
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(notification => notification.remove());
            
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;
            notification.setAttribute('role', type === 'error' ? 'alert' : 'status');
            notification.setAttribute('aria-live', type === 'error' ? 'assertive' : 'polite');

            const colors = {
                info: 'rgba(239, 125, 0, 0.95)',
                success: 'rgba(34, 197, 94, 0.95)',
                error: 'rgba(239, 68, 68, 0.95)'
            };

            Object.assign(notification.style, {
                position: 'fixed',
                top: '20px',
                right: '20px',
                background: colors[type] || colors.info,
                color: 'white',
                padding: '16px 24px',
                borderRadius: '12px',
                boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
                backdropFilter: 'blur(20px)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                zIndex: '1000',
                fontSize: '14px',
                fontWeight: '500',
                transform: 'translateX(100%)',
                transition: 'transform 0.3s ease',
                maxWidth: '300px'
            });
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);
        }
    },
    
    mounted() {
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape') {
                if (confirm('Möchten Sie das Formular wirklich verlassen? Alle eingegebenen Daten gehen verloren.')) {
                    window.location.href = 'index.html';
                }
            }
        });

        const inputs = document.querySelectorAll('.form-input');
        inputs.forEach(input => {
            input.addEventListener('focus', (e) => {
                e.target.closest('.form-group').style.transform = 'translateY(-2px)';
                // Clear any previous validation errors
                e.target.removeAttribute('aria-invalid');
            });
            
            input.addEventListener('blur', (e) => {
                e.target.closest('.form-group').style.transform = '';
            });

            // Add real-time validation for required fields
            input.addEventListener('input', (e) => {
                if (e.target.hasAttribute('required') && e.target.value.trim() !== '') {
                    e.target.removeAttribute('aria-invalid');
                }
            });
        });

        // Add keyboard navigation for info buttons
        const infoButtons = document.querySelectorAll('.info-icon');
        infoButtons.forEach(button => {
            button.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    // Toggle tooltip visibility
                    const tooltipId = button.getAttribute('aria-describedby');
                    if (tooltipId) {
                        const tooltip = document.getElementById(tooltipId);
                        if (tooltip) {
                            const isVisible = tooltip.style.display !== 'none';
                            tooltip.style.display = isVisible ? 'none' : 'block';
                        }
                    }
                }
            });
        });
    }
});

app.mount('#app');